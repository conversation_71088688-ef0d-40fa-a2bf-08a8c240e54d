import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    Alert,
    Platform,
} from 'react-native';
import {
    quickCacheInvalidation,
    forceCacheInvalidation,
    emergencyInvalidation,
    getCacheStats,
    getCurrentVersionInfo,
    hasVersionChanged,
    shouldClearCache,
    isAndroidCacheClearingAvailable,
    getAndroidCacheSize,
} from '../../utils/cacheInvalidation';
import { runCacheInvalidationTests, createTestCacheData, cleanupTestCacheData } from '../../utils/cacheInvalidation/testUtils';

/**
 * Debug screen for testing cache invalidation functionality
 * This screen should only be accessible in development builds
 */
const CacheDebugScreen: React.FC = () => {
    const [cacheStats, setCacheStats] = useState<any>(null);
    const [versionInfo, setVersionInfo] = useState<any>(null);
    const [testResults, setTestResults] = useState<any[]>([]);
    const [loading, setLoading] = useState<string | null>(null);

    useEffect(() => {
        loadInitialData();
    }, []);

    const loadInitialData = async () => {
        try {
            const stats = await getCacheStats();
            const version = getCurrentVersionInfo();
            setCacheStats(stats);
            setVersionInfo(version);
        } catch (error) {
            console.error('Error loading initial data:', error);
        }
    };

    const handleQuickInvalidation = async () => {
        setLoading('quick');
        try {
            const result = await quickCacheInvalidation();
            Alert.alert('Quick Cache Invalidation', JSON.stringify(result, null, 2));
            await loadInitialData();
        } catch (error) {
            Alert.alert('Error', `Quick invalidation failed: ${error}`);
        } finally {
            setLoading(null);
        }
    };

    const handleForceInvalidation = async () => {
        setLoading('force');
        try {
            const result = await forceCacheInvalidation();
            Alert.alert('Force Cache Invalidation', JSON.stringify(result, null, 2));
            await loadInitialData();
        } catch (error) {
            Alert.alert('Error', `Force invalidation failed: ${error}`);
        } finally {
            setLoading(null);
        }
    };

    const handleEmergencyInvalidation = async () => {
        Alert.alert(
            'Emergency Cache Clear',
            'This will clear almost all cached data. Are you sure?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Clear',
                    style: 'destructive',
                    onPress: async () => {
                        setLoading('emergency');
                        try {
                            const result = await emergencyInvalidation();
                            Alert.alert('Emergency Cache Clear', JSON.stringify(result, null, 2));
                            await loadInitialData();
                        } catch (error) {
                            Alert.alert('Error', `Emergency invalidation failed: ${error}`);
                        } finally {
                            setLoading(null);
                        }
                    },
                },
            ]
        );
    };

    const handleRunTests = async () => {
        setLoading('tests');
        try {
            const results = await runCacheInvalidationTests();
            setTestResults(results);
            
            const successCount = results.filter(r => r.success).length;
            Alert.alert(
                'Test Results',
                `${successCount}/${results.length} tests passed. Check the results below.`
            );
        } catch (error) {
            Alert.alert('Error', `Tests failed: ${error}`);
        } finally {
            setLoading(null);
        }
    };

    const handleCreateTestData = async () => {
        try {
            await createTestCacheData();
            Alert.alert('Success', 'Test cache data created');
            await loadInitialData();
        } catch (error) {
            Alert.alert('Error', `Failed to create test data: ${error}`);
        }
    };

    const handleCleanupTestData = async () => {
        try {
            await cleanupTestCacheData();
            Alert.alert('Success', 'Test cache data cleaned up');
            await loadInitialData();
        } catch (error) {
            Alert.alert('Error', `Failed to cleanup test data: ${error}`);
        }
    };

    const handleCheckVersionChange = async () => {
        try {
            const versionChanged = await hasVersionChanged();
            const shouldClear = await shouldClearCache();
            Alert.alert(
                'Version Check',
                `Version changed: ${versionChanged}\nShould clear cache: ${shouldClear}`
            );
        } catch (error) {
            Alert.alert('Error', `Version check failed: ${error}`);
        }
    };

    const handleAndroidCacheSize = async () => {
        if (Platform.OS !== 'android') {
            Alert.alert('Info', 'This feature is only available on Android');
            return;
        }

        try {
            const size = await getAndroidCacheSize();
            const sizeMB = (size / 1024 / 1024).toFixed(2);
            Alert.alert('Android Cache Size', `${sizeMB} MB (${size} bytes)`);
        } catch (error) {
            Alert.alert('Error', `Failed to get cache size: ${error}`);
        }
    };

    const renderButton = (title: string, onPress: () => void, isLoading: boolean = false) => (
        <TouchableOpacity
            style={[styles.button, isLoading && styles.buttonDisabled]}
            onPress={onPress}
            disabled={isLoading}
        >
            <Text style={styles.buttonText}>
                {isLoading ? 'Loading...' : title}
            </Text>
        </TouchableOpacity>
    );

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>Cache Invalidation Debug</Text>
            
            {/* Version Info */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Version Info</Text>
                {versionInfo && (
                    <Text style={styles.info}>
                        Version: {versionInfo.version} ({versionInfo.buildNumber}){'\n'}
                        Platform: {versionInfo.platform}{'\n'}
                        Timestamp: {new Date(versionInfo.timestamp).toLocaleString()}
                    </Text>
                )}
            </View>

            {/* Cache Stats */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Cache Statistics</Text>
                {cacheStats && (
                    <Text style={styles.info}>
                        Total Keys: {cacheStats.totalKeys}{'\n'}
                        Preserved Keys: {cacheStats.preservedKeys}{'\n'}
                        Clearable Keys: {cacheStats.clearableKeys}{'\n'}
                        Problematic Keys: {cacheStats.problematicKeys}
                    </Text>
                )}
            </View>

            {/* Actions */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Cache Actions</Text>
                {renderButton('Quick Invalidation', handleQuickInvalidation, loading === 'quick')}
                {renderButton('Force Invalidation', handleForceInvalidation, loading === 'force')}
                {renderButton('Emergency Clear', handleEmergencyInvalidation, loading === 'emergency')}
                {renderButton('Check Version Change', handleCheckVersionChange)}
                {Platform.OS === 'android' && renderButton('Android Cache Size', handleAndroidCacheSize)}
            </View>

            {/* Test Actions */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Test Actions</Text>
                {renderButton('Run All Tests', handleRunTests, loading === 'tests')}
                {renderButton('Create Test Data', handleCreateTestData)}
                {renderButton('Cleanup Test Data', handleCleanupTestData)}
            </View>

            {/* Test Results */}
            {testResults.length > 0 && (
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Test Results</Text>
                    {testResults.map((result, index) => (
                        <View key={index} style={styles.testResult}>
                            <Text style={[styles.testName, result.success ? styles.testSuccess : styles.testFailure]}>
                                {result.testName}: {result.success ? 'PASS' : 'FAIL'}
                            </Text>
                            {result.error && <Text style={styles.testError}>{result.error}</Text>}
                            <Text style={styles.testDetails}>{JSON.stringify(result.details, null, 2)}</Text>
                        </View>
                    ))}
                </View>
            )}

            {/* Platform Info */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Platform Info</Text>
                <Text style={styles.info}>
                    Platform: {Platform.OS}{'\n'}
                    Android Cache Available: {isAndroidCacheClearingAvailable() ? 'Yes' : 'No'}
                </Text>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: '#f5f5f5',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
        color: '#333',
    },
    section: {
        backgroundColor: 'white',
        padding: 16,
        marginBottom: 16,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12,
        color: '#333',
    },
    info: {
        fontSize: 14,
        color: '#666',
        fontFamily: 'monospace',
    },
    button: {
        backgroundColor: '#007AFF',
        padding: 12,
        borderRadius: 6,
        marginBottom: 8,
        alignItems: 'center',
    },
    buttonDisabled: {
        backgroundColor: '#ccc',
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
    testResult: {
        marginBottom: 12,
        padding: 8,
        backgroundColor: '#f9f9f9',
        borderRadius: 4,
    },
    testName: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    testSuccess: {
        color: '#4CAF50',
    },
    testFailure: {
        color: '#F44336',
    },
    testError: {
        fontSize: 12,
        color: '#F44336',
        marginBottom: 4,
    },
    testDetails: {
        fontSize: 12,
        color: '#666',
        fontFamily: 'monospace',
    },
});

export default CacheDebugScreen;
