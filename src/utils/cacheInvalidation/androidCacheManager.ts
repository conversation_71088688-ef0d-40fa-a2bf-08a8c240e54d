import { NativeModules, Platform } from 'react-native';

interface CacheClearModule {
    clearAndroidCache(): Promise<boolean>;
    clearAppCache(): Promise<boolean>;
    clearWebViewCache(): Promise<boolean>;
    clearTempFiles(): Promise<boolean>;
    getCacheSize(): Promise<number>;
}

const { CacheClearModule } = NativeModules;

/**
 * Android-specific cache clearing utilities
 * These functions only work on Android platform
 */

/**
 * Clear all Android-specific caches
 */
export const clearAllAndroidCache = async (): Promise<boolean> => {
    if (Platform.OS !== 'android') {
        console.log('clearAllAndroidCache: Not on Android platform, skipping');
        return true;
    }

    if (!CacheClearModule) {
        console.warn('CacheClearModule not available');
        return false;
    }

    try {
        console.log('Clearing all Android cache...');
        const result = await CacheClearModule.clearAndroidCache();
        console.log('Android cache cleared successfully');
        return result;
    } catch (error) {
        console.error('Failed to clear Android cache:', error);
        return false;
    }
};

/**
 * Clear Android app cache directory
 */
export const clearAndroidAppCache = async (): Promise<boolean> => {
    if (Platform.OS !== 'android') {
        return true;
    }

    if (!CacheClearModule) {
        console.warn('CacheClearModule not available');
        return false;
    }

    try {
        console.log('Clearing Android app cache...');
        const result = await CacheClearModule.clearAppCache();
        console.log('Android app cache cleared successfully');
        return result;
    } catch (error) {
        console.error('Failed to clear Android app cache:', error);
        return false;
    }
};

/**
 * Clear Android WebView cache
 */
export const clearAndroidWebViewCache = async (): Promise<boolean> => {
    if (Platform.OS !== 'android') {
        return true;
    }

    if (!CacheClearModule) {
        console.warn('CacheClearModule not available');
        return false;
    }

    try {
        console.log('Clearing Android WebView cache...');
        const result = await CacheClearModule.clearWebViewCache();
        console.log('Android WebView cache cleared successfully');
        return result;
    } catch (error) {
        console.error('Failed to clear Android WebView cache:', error);
        return false;
    }
};

/**
 * Clear Android temporary files
 */
export const clearAndroidTempFiles = async (): Promise<boolean> => {
    if (Platform.OS !== 'android') {
        return true;
    }

    if (!CacheClearModule) {
        console.warn('CacheClearModule not available');
        return false;
    }

    try {
        console.log('Clearing Android temp files...');
        const result = await CacheClearModule.clearTempFiles();
        console.log('Android temp files cleared successfully');
        return result;
    } catch (error) {
        console.error('Failed to clear Android temp files:', error);
        return false;
    }
};

/**
 * Get Android cache size in bytes
 */
export const getAndroidCacheSize = async (): Promise<number> => {
    if (Platform.OS !== 'android') {
        return 0;
    }

    if (!CacheClearModule) {
        console.warn('CacheClearModule not available');
        return 0;
    }

    try {
        const size = await CacheClearModule.getCacheSize();
        console.log(`Android cache size: ${(size / 1024 / 1024).toFixed(2)} MB`);
        return size;
    } catch (error) {
        console.error('Failed to get Android cache size:', error);
        return 0;
    }
};

/**
 * Comprehensive Android cache clearing for app updates
 * This is the main function to call when dealing with stale cache issues
 */
export const performAndroidCacheInvalidation = async (): Promise<{
    success: boolean;
    clearedCaches: string[];
    errors: string[];
}> => {
    if (Platform.OS !== 'android') {
        return {
            success: true,
            clearedCaches: [],
            errors: ['Not on Android platform'],
        };
    }

    const clearedCaches: string[] = [];
    const errors: string[] = [];

    console.log('Starting comprehensive Android cache invalidation...');

    // Get initial cache size
    try {
        const initialSize = await getAndroidCacheSize();
        console.log(`Initial cache size: ${(initialSize / 1024 / 1024).toFixed(2)} MB`);
    } catch (error) {
        console.warn('Could not get initial cache size');
    }

    // Clear app cache
    try {
        const success = await clearAndroidAppCache();
        if (success) {
            clearedCaches.push('App Cache');
        } else {
            errors.push('Failed to clear app cache');
        }
    } catch (error) {
        errors.push(`App cache error: ${error}`);
    }

    // Clear WebView cache
    try {
        const success = await clearAndroidWebViewCache();
        if (success) {
            clearedCaches.push('WebView Cache');
        } else {
            errors.push('Failed to clear WebView cache');
        }
    } catch (error) {
        errors.push(`WebView cache error: ${error}`);
    }

    // Clear temp files
    try {
        const success = await clearAndroidTempFiles();
        if (success) {
            clearedCaches.push('Temp Files');
        } else {
            errors.push('Failed to clear temp files');
        }
    } catch (error) {
        errors.push(`Temp files error: ${error}`);
    }

    // Get final cache size
    try {
        const finalSize = await getAndroidCacheSize();
        console.log(`Final cache size: ${(finalSize / 1024 / 1024).toFixed(2)} MB`);
    } catch (error) {
        console.warn('Could not get final cache size');
    }

    const success = errors.length === 0;
    
    console.log('Android cache invalidation completed:', {
        success,
        clearedCaches,
        errors,
    });

    return {
        success,
        clearedCaches,
        errors,
    };
};

/**
 * Check if Android cache clearing is available
 */
export const isAndroidCacheClearingAvailable = (): boolean => {
    return Platform.OS === 'android' && !!CacheClearModule;
};
