import AsyncStorage from '@react-native-async-storage/async-storage';
import { getVersion, getBuildNumber } from 'react-native-device-info';
import { Platform } from 'react-native';

// Storage keys for version tracking
const VERSION_STORAGE_KEY = 'app_version_cache_key';
const BUILD_STORAGE_KEY = 'app_build_cache_key';
const LAST_CACHE_CLEAR_KEY = 'last_cache_clear_timestamp';

export interface VersionInfo {
    version: string;
    buildNumber: string;
    platform: string;
    timestamp: number;
}

/**
 * Get current app version information
 */
export const getCurrentVersionInfo = (): VersionInfo => {
    return {
        version: getVersion(),
        buildNumber: getBuildNumber(),
        platform: Platform.OS,
        timestamp: Date.now(),
    };
};

/**
 * Get stored version information from AsyncStorage
 */
export const getStoredVersionInfo = async (): Promise<VersionInfo | null> => {
    try {
        const storedVersion = await AsyncStorage.getItem(VERSION_STORAGE_KEY);
        const storedBuild = await AsyncStorage.getItem(BUILD_STORAGE_KEY);
        const lastClearTimestamp = await AsyncStorage.getItem(LAST_CACHE_CLEAR_KEY);

        if (storedVersion && storedBuild) {
            return {
                version: storedVersion,
                buildNumber: storedBuild,
                platform: Platform.OS,
                timestamp: lastClearTimestamp ? parseInt(lastClearTimestamp, 10) : 0,
            };
        }
        return null;
    } catch (error) {
        console.error('Error getting stored version info:', error);
        return null;
    }
};

/**
 * Store current version information to AsyncStorage
 */
export const storeCurrentVersionInfo = async (): Promise<void> => {
    try {
        const currentInfo = getCurrentVersionInfo();
        await AsyncStorage.setItem(VERSION_STORAGE_KEY, currentInfo.version);
        await AsyncStorage.setItem(BUILD_STORAGE_KEY, currentInfo.buildNumber);
        await AsyncStorage.setItem(LAST_CACHE_CLEAR_KEY, currentInfo.timestamp.toString());
        console.log('Stored version info:', currentInfo);
    } catch (error) {
        console.error('Error storing version info:', error);
    }
};

/**
 * Check if app version has changed since last launch
 */
export const hasVersionChanged = async (): Promise<boolean> => {
    try {
        const currentInfo = getCurrentVersionInfo();
        const storedInfo = await getStoredVersionInfo();

        if (!storedInfo) {
            // First launch or no stored version info
            console.log('No stored version info found - treating as version change');
            return true;
        }

        const versionChanged = currentInfo.version !== storedInfo.version;
        const buildChanged = currentInfo.buildNumber !== storedInfo.buildNumber;

        if (versionChanged || buildChanged) {
            console.log('Version change detected:', {
                current: `${currentInfo.version} (${currentInfo.buildNumber})`,
                stored: `${storedInfo.version} (${storedInfo.buildNumber})`,
            });
            return true;
        }

        return false;
    } catch (error) {
        console.error('Error checking version change:', error);
        // On error, assume version changed to be safe
        return true;
    }
};

/**
 * Check if cache clearing is needed based on version change or time threshold
 */
export const shouldClearCache = async (maxCacheAgeHours: number = 168): Promise<boolean> => {
    try {
        // Check for version change first
        const versionChanged = await hasVersionChanged();
        if (versionChanged) {
            return true;
        }

        // Check cache age (default 7 days = 168 hours)
        const storedInfo = await getStoredVersionInfo();
        if (storedInfo && storedInfo.timestamp) {
            const ageInHours = (Date.now() - storedInfo.timestamp) / (1000 * 60 * 60);
            if (ageInHours > maxCacheAgeHours) {
                console.log(`Cache age (${ageInHours.toFixed(1)} hours) exceeds threshold (${maxCacheAgeHours} hours)`);
                return true;
            }
        }

        return false;
    } catch (error) {
        console.error('Error checking if cache should be cleared:', error);
        return false;
    }
};

/**
 * Reset version tracking (useful for testing)
 */
export const resetVersionTracking = async (): Promise<void> => {
    try {
        await AsyncStorage.removeItem(VERSION_STORAGE_KEY);
        await AsyncStorage.removeItem(BUILD_STORAGE_KEY);
        await AsyncStorage.removeItem(LAST_CACHE_CLEAR_KEY);
        console.log('Version tracking reset');
    } catch (error) {
        console.error('Error resetting version tracking:', error);
    }
};
