# Cache Invalidation System

This system automatically handles cache invalidation when the app version changes, preventing users from needing to delete and reinstall the app after updates.

## Problem Solved

Users were experiencing "bricked" apps after updates due to stale cache data. They had to:
1. Delete the app
2. Restart their phone  
3. Reinstall the app

This cache invalidation system eliminates this need by automatically clearing stale data when a new version is detected.

## Features

- **Automatic Version Detection**: Tracks app version and build number changes
- **Selective Cache Clearing**: Preserves essential data while clearing problematic cache
- **Android-Specific Optimizations**: Native Android cache clearing for better results
- **Multiple Strategies**: Different clearing strategies based on needs
- **Comprehensive Testing**: Built-in test utilities for validation

## Usage

### Basic Integration

The system is automatically integrated into the app startup process:

```typescript
// In App.tsx - automatically runs on startup
import { quickCacheInvalidation } from './src/utils/cacheInvalidation';

useEffect(() => {
    const performCacheInvalidation = async () => {
        const result = await quickCacheInvalidation();
        if (result.wasCleared) {
            console.log(`Cache cleared: ${result.reason}`);
        }
    };
    performCacheInvalidation();
}, []);
```

### Manual Cache Clearing

```typescript
import { 
    quickCacheInvalidation,
    forceCacheInvalidation,
    emergencyInvalidation 
} from './src/utils/cacheInvalidation';

// Quick invalidation (recommended for normal use)
const result = await quickCacheInvalidation();

// Force invalidation (clears everything)
const result = await forceCacheInvalidation();

// Emergency invalidation (minimal preservation)
const result = await emergencyInvalidation();
```

### Android-Specific Features

```typescript
import { 
    performAndroidCacheInvalidation,
    getAndroidCacheSize,
    isAndroidCacheClearingAvailable 
} from './src/utils/cacheInvalidation';

// Check if Android features are available
if (isAndroidCacheClearingAvailable()) {
    // Get cache size
    const size = await getAndroidCacheSize();
    
    // Perform comprehensive Android cache clearing
    const result = await performAndroidCacheInvalidation();
}
```

## Cache Clearing Strategies

### 1. Selective (Default)
- Clears problematic cache patterns
- Preserves essential user data
- Best for regular app updates

### 2. Full
- Clears all cache except preserved keys
- More thorough cleaning
- Use when selective isn't sufficient

### 3. Problematic
- Only clears known problematic keys
- Minimal impact on user experience
- Use for minor updates

### 4. Emergency
- Clears almost everything
- Only preserves critical data
- Use as last resort

## Preserved Data

The following data is always preserved during cache clearing:

- `customDeviceId` - Device identification
- `hasLaunchedIOS` - iOS first launch tracking
- `app_version_cache_key` - Version tracking
- `app_build_cache_key` - Build tracking
- `last_cache_clear_timestamp` - Cache clear history
- `tg-admin-id` - Admin user identification
- `is-admin` - Admin status

## Android Native Features

The system includes native Android modules for comprehensive cache clearing:

### CacheClearModule.kt
- Clears app cache directories
- Clears WebView cache
- Clears SharedPreferences (selectively)
- Clears temporary files
- Provides cache size information

### Integration
The module is automatically registered in `MainApplication.kt`:

```kotlin
add(CacheClearPackage()) // Add CacheClearPackage for Android cache management
```

## Testing

### Debug Screen
A debug screen is available for testing cache invalidation:

```typescript
import CacheDebugScreen from './src/screens/debug/CacheDebugScreen';
```

### Test Utilities

```typescript
import { 
    runCacheInvalidationTests,
    createTestCacheData,
    cleanupTestCacheData 
} from './src/utils/cacheInvalidation/testUtils';

// Run comprehensive tests
const results = await runCacheInvalidationTests();

// Create test data for testing
await createTestCacheData();

// Clean up test data
await cleanupTestCacheData();
```

## Configuration

### Version Tracking
The system automatically tracks:
- App version (from react-native-device-info)
- Build number (from react-native-device-info)
- Platform (iOS/Android)
- Last cache clear timestamp

### Cache Age Threshold
Default: 7 days (168 hours)

```typescript
const result = await performCacheInvalidation({
    strategy: 'selective',
    maxCacheAgeHours: 168, // 7 days
    platformSpecific: true,
});
```

## Troubleshooting

### Common Issues

1. **Cache not clearing on Android**
   - Ensure CacheClearPackage is registered in MainApplication.kt
   - Check if native module is available with `isAndroidCacheClearingAvailable()`

2. **Essential data being cleared**
   - Check if keys are in the PRESERVED_KEYS list
   - Use selective strategy instead of full/emergency

3. **Version change not detected**
   - Verify app version and build number are updating correctly
   - Check version tracking with `getCurrentVersionInfo()`

### Debug Information

Enable verbose logging in development:

```typescript
const result = await performCacheInvalidation({
    strategy: 'selective',
    verbose: __DEV__, // Enable in development
});
```

### Manual Reset

To reset version tracking (for testing):

```typescript
import { resetVersionTracking } from './src/utils/cacheInvalidation';
await resetVersionTracking();
```

## Performance Impact

- **Startup Impact**: Minimal (< 100ms typically)
- **Cache Clearing**: Runs asynchronously, doesn't block UI
- **Storage Impact**: Reduces storage usage by clearing stale data
- **Network Impact**: May trigger fresh data fetching after clearing

## Best Practices

1. **Use Quick Invalidation**: For most cases, `quickCacheInvalidation()` is sufficient
2. **Monitor Results**: Log cache invalidation results for debugging
3. **Test Thoroughly**: Use the debug screen and test utilities during development
4. **Platform-Specific**: Enable Android-specific clearing for better results
5. **Preserve User Data**: Always ensure essential user data is preserved

## Future Enhancements

- iOS-specific native cache clearing
- Configurable preservation rules
- Cache warming after clearing
- Analytics integration for cache invalidation metrics
- Automatic cache health monitoring
