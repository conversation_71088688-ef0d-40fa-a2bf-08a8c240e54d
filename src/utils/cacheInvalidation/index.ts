import { Platform } from 'react-native';
import { shouldClearCache, hasVersionChanged, getCurrentVersionInfo } from './versionManager';
import { 
    clearStaleCache, 
    clearSelectiveCache, 
    clearAndroidSpecificCache, 
    clearProblematicCache,
    emergencyCacheClear,
    getCacheStats 
} from './cacheManager';

export interface CacheInvalidationOptions {
    // Strategy for cache clearing
    strategy: 'full' | 'selective' | 'problematic' | 'emergency';
    
    // Maximum cache age in hours before forcing clear (default: 7 days)
    maxCacheAgeHours?: number;
    
    // Whether to perform platform-specific clearing
    platformSpecific?: boolean;
    
    // Whether to log detailed information
    verbose?: boolean;
}

export interface CacheInvalidationResult {
    wasCleared: boolean;
    reason: string;
    versionChanged: boolean;
    currentVersion: string;
    strategy: string;
    error?: string;
}

/**
 * Main cache invalidation function
 * This should be called during app initialization
 */
export const performCacheInvalidation = async (
    options: CacheInvalidationOptions = { strategy: 'selective' }
): Promise<CacheInvalidationResult> => {
    const startTime = Date.now();
    const currentVersion = getCurrentVersionInfo();
    
    try {
        if (options.verbose) {
            console.log('=== Cache Invalidation Started ===');
            console.log('Current version:', currentVersion);
            console.log('Options:', options);
            
            const stats = await getCacheStats();
            console.log('Cache stats:', stats);
        }
        
        // Check if cache clearing is needed
        const shouldClear = await shouldClearCache(options.maxCacheAgeHours);
        const versionChanged = await hasVersionChanged();
        
        if (!shouldClear && options.strategy !== 'emergency') {
            if (options.verbose) {
                console.log('Cache invalidation not needed');
            }
            
            return {
                wasCleared: false,
                reason: 'Cache is up to date',
                versionChanged: false,
                currentVersion: `${currentVersion.version} (${currentVersion.buildNumber})`,
                strategy: options.strategy,
            };
        }
        
        // Determine clearing reason
        let reason = '';
        if (versionChanged) {
            reason = 'App version changed';
        } else if (options.strategy === 'emergency') {
            reason = 'Emergency cache clear requested';
        } else {
            reason = 'Cache age threshold exceeded';
        }
        
        if (options.verbose) {
            console.log(`Cache clearing needed: ${reason}`);
        }
        
        // Perform cache clearing based on strategy
        switch (options.strategy) {
            case 'full':
                await clearStaleCache();
                break;
                
            case 'selective':
                await clearSelectiveCache();
                break;
                
            case 'problematic':
                await clearProblematicCache();
                break;
                
            case 'emergency':
                await emergencyCacheClear();
                break;
                
            default:
                await clearSelectiveCache();
        }
        
        // Perform platform-specific clearing if requested
        if (options.platformSpecific) {
            if (Platform.OS === 'android') {
                await clearAndroidSpecificCache();
            }
            // Add iOS-specific clearing if needed in the future
        }
        
        const duration = Date.now() - startTime;
        
        if (options.verbose) {
            console.log(`=== Cache Invalidation Completed in ${duration}ms ===`);
            
            const finalStats = await getCacheStats();
            console.log('Final cache stats:', finalStats);
        }
        
        return {
            wasCleared: true,
            reason,
            versionChanged,
            currentVersion: `${currentVersion.version} (${currentVersion.buildNumber})`,
            strategy: options.strategy,
        };
        
    } catch (error) {
        console.error('Cache invalidation failed:', error);
        
        return {
            wasCleared: false,
            reason: 'Cache invalidation failed',
            versionChanged: false,
            currentVersion: `${currentVersion.version} (${currentVersion.buildNumber})`,
            strategy: options.strategy,
            error: error instanceof Error ? error.message : 'Unknown error',
        };
    }
};

/**
 * Quick cache invalidation for app startup
 * Uses selective strategy with Android-specific optimizations
 */
export const quickCacheInvalidation = async (): Promise<CacheInvalidationResult> => {
    return performCacheInvalidation({
        strategy: 'selective',
        maxCacheAgeHours: 168, // 7 days
        platformSpecific: true,
        verbose: __DEV__, // Only verbose in development
    });
};

/**
 * Force cache invalidation (for troubleshooting)
 * Uses full strategy to clear everything
 */
export const forceCacheInvalidation = async (): Promise<CacheInvalidationResult> => {
    return performCacheInvalidation({
        strategy: 'full',
        maxCacheAgeHours: 0, // Force clear regardless of age
        platformSpecific: true,
        verbose: true,
    });
};

/**
 * Emergency cache invalidation (for critical issues)
 * Clears almost everything except essential data
 */
export const emergencyInvalidation = async (): Promise<CacheInvalidationResult> => {
    return performCacheInvalidation({
        strategy: 'emergency',
        maxCacheAgeHours: 0,
        platformSpecific: true,
        verbose: true,
    });
};

// Export individual functions for advanced usage
export {
    shouldClearCache,
    hasVersionChanged,
    getCurrentVersionInfo,
} from './versionManager';

export {
    clearStaleCache as clearCache,
    clearStaleCache,
    clearSelectiveCache,
    clearAndroidSpecificCache,
    clearProblematicCache,
    emergencyCacheClear,
    getCacheStats,
} from './cacheManager';

export {
    clearAllAndroidCache,
    clearAndroidAppCache,
    clearAndroidWebViewCache,
    clearAndroidTempFiles,
    getAndroidCacheSize,
    performAndroidCacheInvalidation,
    isAndroidCacheClearingAvailable,
} from './androidCacheManager';
