import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { storeCurrentVersionInfo } from './versionManager';
import { performAndroidCacheInvalidation } from './androidCacheManager';

// Keys that should be preserved during cache clearing
const PRESERVED_KEYS = [
    // Version tracking
    'app_version_cache_key',
    'app_build_cache_key',
    'last_cache_clear_timestamp',
    
    // Essential user data that should persist
    'customDeviceId',
    'hasLaunchedIOS',
    
    // Authentication related (preserve to avoid forced re-login)
    // Note: Firebase auth handles its own persistence
    
    // Admin related (preserve admin status)
    'tg-admin-id',
    'is-admin',
];

// Keys that should always be cleared (known problematic cache)
const FORCE_CLEAR_KEYS = [
    'tg-user', // User data cache that often becomes stale
    // Add other known problematic keys here
];

/**
 * Get all AsyncStorage keys
 */
const getAllStorageKeys = async (): Promise<string[]> => {
    try {
        return await AsyncStorage.getAllKeys();
    } catch (error) {
        console.error('Error getting all storage keys:', error);
        return [];
    }
};

/**
 * Clear specific AsyncStorage keys
 */
const clearSpecificKeys = async (keys: string[]): Promise<void> => {
    try {
        if (keys.length > 0) {
            await AsyncStorage.multiRemove(keys);
            console.log(`Cleared ${keys.length} cache keys:`, keys);
        }
    } catch (error) {
        console.error('Error clearing specific keys:', error);
    }
};

/**
 * Clear all cache except preserved keys
 */
export const clearStaleCache = async (): Promise<void> => {
    try {
        console.log('Starting cache invalidation...');
        
        const allKeys = await getAllStorageKeys();
        console.log(`Found ${allKeys.length} total storage keys`);
        
        // Determine keys to clear
        const keysToClear = allKeys.filter(key => !PRESERVED_KEYS.includes(key));
        
        if (keysToClear.length > 0) {
            await clearSpecificKeys(keysToClear);
            console.log(`Cache clearing completed. Cleared ${keysToClear.length} keys, preserved ${PRESERVED_KEYS.length} keys`);
        } else {
            console.log('No keys to clear');
        }
        
        // Update version info after successful cache clear
        await storeCurrentVersionInfo();
        
    } catch (error) {
        console.error('Error during cache invalidation:', error);
        // Still try to update version info even if clearing failed
        try {
            await storeCurrentVersionInfo();
        } catch (versionError) {
            console.error('Error updating version info after failed cache clear:', versionError);
        }
    }
};

/**
 * Force clear specific problematic keys
 */
export const clearProblematicCache = async (): Promise<void> => {
    try {
        console.log('Clearing known problematic cache keys...');
        await clearSpecificKeys(FORCE_CLEAR_KEYS);
        console.log('Problematic cache keys cleared');
    } catch (error) {
        console.error('Error clearing problematic cache:', error);
    }
};

/**
 * Selective cache clearing - only clear data that commonly causes issues
 */
export const clearSelectiveCache = async (): Promise<void> => {
    try {
        console.log('Starting selective cache clearing...');
        
        const allKeys = await getAllStorageKeys();
        
        // Define patterns for keys that commonly cause issues
        const problematicPatterns = [
            /^tg-/, // All TG-prefixed keys except preserved ones
            /cache/i, // Any key containing 'cache'
            /temp/i, // Any temporary data
            /session/i, // Session data
            /state/i, // App state that might be stale
        ];
        
        const keysToClear = allKeys.filter(key => {
            // Don't clear preserved keys
            if (PRESERVED_KEYS.includes(key)) {
                return false;
            }
            
            // Clear keys matching problematic patterns
            return problematicPatterns.some(pattern => pattern.test(key));
        });
        
        // Always include force clear keys
        FORCE_CLEAR_KEYS.forEach(key => {
            if (allKeys.includes(key) && !keysToClear.includes(key)) {
                keysToClear.push(key);
            }
        });
        
        if (keysToClear.length > 0) {
            await clearSpecificKeys(keysToClear);
            console.log(`Selective cache clearing completed. Cleared ${keysToClear.length} keys`);
        } else {
            console.log('No problematic keys found to clear');
        }
        
        await storeCurrentVersionInfo();
        
    } catch (error) {
        console.error('Error during selective cache clearing:', error);
    }
};

/**
 * Android-specific cache clearing
 */
export const clearAndroidSpecificCache = async (): Promise<void> => {
    if (Platform.OS !== 'android') {
        return;
    }

    try {
        console.log('Performing Android-specific cache clearing...');

        // Clear Android-specific problematic keys
        const androidSpecificKeys = [
            // Add any Android-specific cache keys that cause issues
        ];

        await clearSpecificKeys(androidSpecificKeys);

        // Force clear user data cache which is particularly problematic on Android
        await clearProblematicCache();

        // Use native Android cache clearing
        try {
            const result = await performAndroidCacheInvalidation();
            if (result.success) {
                console.log('Native Android cache clearing successful:', result.clearedCaches);
            } else {
                console.warn('Native Android cache clearing had errors:', result.errors);
            }
        } catch (error) {
            console.warn('Native Android cache clearing not available or failed:', error);
        }

        console.log('Android-specific cache clearing completed');

    } catch (error) {
        console.error('Error during Android-specific cache clearing:', error);
    }
};

/**
 * Emergency cache clear - clears everything except critical preserved keys
 */
export const emergencyCacheClear = async (): Promise<void> => {
    try {
        console.log('Performing emergency cache clear...');
        
        // Minimal set of keys to preserve in emergency
        const emergencyPreservedKeys = [
            'app_version_cache_key',
            'app_build_cache_key',
            'last_cache_clear_timestamp',
            'customDeviceId',
            'hasLaunchedIOS',
        ];
        
        const allKeys = await getAllStorageKeys();
        const keysToClear = allKeys.filter(key => !emergencyPreservedKeys.includes(key));
        
        await clearSpecificKeys(keysToClear);
        await storeCurrentVersionInfo();
        
        console.log(`Emergency cache clear completed. Cleared ${keysToClear.length} keys`);
        
    } catch (error) {
        console.error('Error during emergency cache clear:', error);
    }
};

/**
 * Get cache statistics
 */
export const getCacheStats = async (): Promise<{
    totalKeys: number;
    preservedKeys: number;
    clearableKeys: number;
    problematicKeys: number;
}> => {
    try {
        const allKeys = await getAllStorageKeys();
        const preservedKeys = allKeys.filter(key => PRESERVED_KEYS.includes(key));
        const clearableKeys = allKeys.filter(key => !PRESERVED_KEYS.includes(key));
        const problematicKeys = allKeys.filter(key => FORCE_CLEAR_KEYS.includes(key));
        
        return {
            totalKeys: allKeys.length,
            preservedKeys: preservedKeys.length,
            clearableKeys: clearableKeys.length,
            problematicKeys: problematicKeys.length,
        };
    } catch (error) {
        console.error('Error getting cache stats:', error);
        return {
            totalKeys: 0,
            preservedKeys: 0,
            clearableKeys: 0,
            problematicKeys: 0,
        };
    }
};
