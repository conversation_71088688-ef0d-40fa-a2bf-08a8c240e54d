import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import {
    performCacheInvalidation,
    quickCacheInvalidation,
    forceCacheInvalidation,
    emergencyInvalidation,
    getCacheStats,
    getCurrentVersionInfo,
    hasVersionChanged,
    shouldClearCache,
    resetVersionTracking,
    isAndroidCacheClearingAvailable,
    getAndroidCacheSize,
} from './index';

/**
 * Test utilities for cache invalidation system
 * These functions are for development and testing purposes
 */

export interface CacheTestResult {
    testName: string;
    success: boolean;
    details: any;
    error?: string;
}

/**
 * Run comprehensive cache invalidation tests
 */
export const runCacheInvalidationTests = async (): Promise<CacheTestResult[]> => {
    const results: CacheTestResult[] = [];
    
    console.log('=== Starting Cache Invalidation Tests ===');
    
    // Test 1: Basic version info
    results.push(await testVersionInfo());
    
    // Test 2: Cache stats
    results.push(await testCacheStats());
    
    // Test 3: Version change detection
    results.push(await testVersionChangeDetection());
    
    // Test 4: Quick cache invalidation
    results.push(await testQuickCacheInvalidation());
    
    // Test 5: Selective cache clearing
    results.push(await testSelectiveCacheClearing());
    
    // Test 6: Android-specific tests (if on Android)
    if (Platform.OS === 'android') {
        results.push(await testAndroidSpecificFeatures());
    }
    
    // Test 7: Storage preservation
    results.push(await testStoragePreservation());
    
    console.log('=== Cache Invalidation Tests Completed ===');
    
    const successCount = results.filter(r => r.success).length;
    console.log(`Tests passed: ${successCount}/${results.length}`);
    
    return results;
};

/**
 * Test version info functionality
 */
const testVersionInfo = async (): Promise<CacheTestResult> => {
    try {
        const versionInfo = getCurrentVersionInfo();
        
        const isValid = versionInfo.version && 
                       versionInfo.buildNumber && 
                       versionInfo.platform && 
                       versionInfo.timestamp > 0;
        
        return {
            testName: 'Version Info',
            success: isValid,
            details: versionInfo,
            error: isValid ? undefined : 'Invalid version info structure',
        };
    } catch (error) {
        return {
            testName: 'Version Info',
            success: false,
            details: null,
            error: error instanceof Error ? error.message : 'Unknown error',
        };
    }
};

/**
 * Test cache statistics
 */
const testCacheStats = async (): Promise<CacheTestResult> => {
    try {
        const stats = await getCacheStats();
        
        const isValid = typeof stats.totalKeys === 'number' &&
                       typeof stats.preservedKeys === 'number' &&
                       typeof stats.clearableKeys === 'number' &&
                       typeof stats.problematicKeys === 'number';
        
        return {
            testName: 'Cache Stats',
            success: isValid,
            details: stats,
            error: isValid ? undefined : 'Invalid cache stats structure',
        };
    } catch (error) {
        return {
            testName: 'Cache Stats',
            success: false,
            details: null,
            error: error instanceof Error ? error.message : 'Unknown error',
        };
    }
};

/**
 * Test version change detection
 */
const testVersionChangeDetection = async (): Promise<CacheTestResult> => {
    try {
        // Reset version tracking to simulate first launch
        await resetVersionTracking();
        
        // Check if version change is detected (should be true for first launch)
        const versionChanged = await hasVersionChanged();
        const shouldClear = await shouldClearCache();
        
        return {
            testName: 'Version Change Detection',
            success: versionChanged && shouldClear,
            details: { versionChanged, shouldClear },
            error: (!versionChanged || !shouldClear) ? 'Version change not detected properly' : undefined,
        };
    } catch (error) {
        return {
            testName: 'Version Change Detection',
            success: false,
            details: null,
            error: error instanceof Error ? error.message : 'Unknown error',
        };
    }
};

/**
 * Test quick cache invalidation
 */
const testQuickCacheInvalidation = async (): Promise<CacheTestResult> => {
    try {
        const result = await quickCacheInvalidation();
        
        const isValid = typeof result.wasCleared === 'boolean' &&
                       typeof result.reason === 'string' &&
                       typeof result.versionChanged === 'boolean' &&
                       typeof result.currentVersion === 'string' &&
                       typeof result.strategy === 'string';
        
        return {
            testName: 'Quick Cache Invalidation',
            success: isValid,
            details: result,
            error: isValid ? undefined : 'Invalid cache invalidation result structure',
        };
    } catch (error) {
        return {
            testName: 'Quick Cache Invalidation',
            success: false,
            details: null,
            error: error instanceof Error ? error.message : 'Unknown error',
        };
    }
};

/**
 * Test selective cache clearing
 */
const testSelectiveCacheClearing = async (): Promise<CacheTestResult> => {
    try {
        // Add some test data
        await AsyncStorage.setItem('test_cache_key', 'test_value');
        await AsyncStorage.setItem('tg-test-key', 'test_value');
        
        // Perform selective cache clearing
        const result = await performCacheInvalidation({ strategy: 'selective' });
        
        // Check if test keys were cleared appropriately
        const testKey = await AsyncStorage.getItem('test_cache_key');
        const tgTestKey = await AsyncStorage.getItem('tg-test-key');
        
        return {
            testName: 'Selective Cache Clearing',
            success: result.wasCleared,
            details: { 
                result, 
                testKeyCleared: testKey === null,
                tgTestKeyCleared: tgTestKey === null,
            },
        };
    } catch (error) {
        return {
            testName: 'Selective Cache Clearing',
            success: false,
            details: null,
            error: error instanceof Error ? error.message : 'Unknown error',
        };
    }
};

/**
 * Test Android-specific features
 */
const testAndroidSpecificFeatures = async (): Promise<CacheTestResult> => {
    try {
        const isAvailable = isAndroidCacheClearingAvailable();
        
        if (!isAvailable) {
            return {
                testName: 'Android Specific Features',
                success: false,
                details: { available: false },
                error: 'Android cache clearing not available',
            };
        }
        
        const cacheSize = await getAndroidCacheSize();
        
        return {
            testName: 'Android Specific Features',
            success: true,
            details: { 
                available: true,
                cacheSize,
                cacheSizeMB: (cacheSize / 1024 / 1024).toFixed(2),
            },
        };
    } catch (error) {
        return {
            testName: 'Android Specific Features',
            success: false,
            details: null,
            error: error instanceof Error ? error.message : 'Unknown error',
        };
    }
};

/**
 * Test storage preservation
 */
const testStoragePreservation = async (): Promise<CacheTestResult> => {
    try {
        // Set some essential data that should be preserved
        await AsyncStorage.setItem('customDeviceId', 'test-device-id');
        await AsyncStorage.setItem('hasLaunchedIOS', 'true');
        
        // Perform cache invalidation
        await performCacheInvalidation({ strategy: 'full' });
        
        // Check if essential data was preserved
        const deviceId = await AsyncStorage.getItem('customDeviceId');
        const hasLaunched = await AsyncStorage.getItem('hasLaunchedIOS');
        
        const preserved = deviceId === 'test-device-id' && hasLaunched === 'true';
        
        return {
            testName: 'Storage Preservation',
            success: preserved,
            details: { 
                deviceIdPreserved: deviceId === 'test-device-id',
                hasLaunchedPreserved: hasLaunched === 'true',
            },
            error: preserved ? undefined : 'Essential data was not preserved',
        };
    } catch (error) {
        return {
            testName: 'Storage Preservation',
            success: false,
            details: null,
            error: error instanceof Error ? error.message : 'Unknown error',
        };
    }
};

/**
 * Create test data for cache invalidation testing
 */
export const createTestCacheData = async (): Promise<void> => {
    try {
        console.log('Creating test cache data...');
        
        // Create various types of test data
        await AsyncStorage.setItem('test_user_data', JSON.stringify({ id: 'test', name: 'Test User' }));
        await AsyncStorage.setItem('test_cache_timestamp', Date.now().toString());
        await AsyncStorage.setItem('tg-test-cache', 'test-value');
        await AsyncStorage.setItem('session_test_data', 'session-value');
        await AsyncStorage.setItem('temp_test_data', 'temp-value');
        
        console.log('Test cache data created');
    } catch (error) {
        console.error('Error creating test cache data:', error);
    }
};

/**
 * Clean up test data
 */
export const cleanupTestCacheData = async (): Promise<void> => {
    try {
        console.log('Cleaning up test cache data...');
        
        const testKeys = [
            'test_user_data',
            'test_cache_timestamp',
            'tg-test-cache',
            'session_test_data',
            'temp_test_data',
            'test_cache_key',
            'tg-test-key',
        ];
        
        await AsyncStorage.multiRemove(testKeys);
        
        console.log('Test cache data cleaned up');
    } catch (error) {
        console.error('Error cleaning up test cache data:', error);
    }
};

/**
 * Simple test runner for development
 */
export const runQuickTest = async (): Promise<void> => {
    console.log('Running quick cache invalidation test...');
    
    try {
        await createTestCacheData();
        const result = await quickCacheInvalidation();
        console.log('Quick test result:', result);
        await cleanupTestCacheData();
    } catch (error) {
        console.error('Quick test failed:', error);
    }
};
